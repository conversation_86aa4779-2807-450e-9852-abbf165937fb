use walkers::{HttpTiles, Map, MapMemory, sources::OpenStreetMap, sources::Mapbox, lon_lat};
use egui::{Context, CentralPanel};
use eframe::{App, Frame};

#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

#[cfg(target_arch = "wasm32")]
use web_sys::js_sys;

#[cfg(not(target_arch = "wasm32"))]
use std::sync::{mpsc, Arc};

// Authentication state structure
#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct AuthState {
    pub is_authenticated: bool,
    pub username: Option<String>,
    pub email: Option<String>,
    pub token: Option<String>,
}

impl Default for AuthState {
    fn default() -> Self {
        Self {
            is_authenticated: false,
            username: None,
            email: None,
            token: None,
        }
    }
}

/// Shared data that all tabs can access and modify
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct SharedData {
    pub counter: i32,
    pub text: String,
    pub slider_value: f32,
    pub checkbox_state: bool,
    pub items: Vec<String>,

    // Authentication state
    pub auth_state: AuthState,

    // Map-related shared state
    #[serde(skip)]
    pub tiles: Option<HttpTiles>,
    #[serde(skip)]
    pub map_memory: MapMemory,

    // Native authentication
    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub login_in_progress: bool,

    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub auth_receiver: Option<mpsc::Receiver<Result<AuthState, String>>>,
}

impl Default for SharedData {
    fn default() -> Self {
        Self {
            counter: 0,
            text: "Shared text across tabs".to_owned(),
            slider_value: 5.0,
            checkbox_state: false,
            items: vec!["Item 1".to_owned(), "Item 2".to_owned()],
            auth_state: AuthState::default(),
            tiles: None,
            map_memory: MapMemory::default(),
            #[cfg(not(target_arch = "wasm32"))]
            login_in_progress: false,
            #[cfg(not(target_arch = "wasm32"))]
            auth_receiver: None,
        }
    }
}

/// Available tabs/views
#[derive(Clone, Copy, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum Tab {
    Map,
    Counter,
    TextEditor,
    DataList,
}

impl Default for Tab {
    fn default() -> Self {
        Tab::Counter
    }
}

/// Theme preference
#[derive(Debug, Clone, Copy, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum ThemePreference {
    Light,
    Dark,
    System, // Follow system theme
}

impl Default for ThemePreference {
    fn default() -> Self {
        ThemePreference::System
    }
}

impl Tab {
    fn name(&self) -> &'static str {
        match self {
            Tab::Map => "Map",
            Tab::Counter => "Counter",
            Tab::TextEditor => "Text Editor",
            Tab::DataList => "Data List",
        }
    }
}

/// Main application with tab-based UI
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct WardaFrontend {
    pub shared_data: SharedData,
    pub current_tab: Tab,

    // UI state
    pub show_preferences: bool,

    // Preferences
    pub theme_preference: ThemePreference,

    // Theme application tracking (not persisted)
    #[serde(skip)]
    pub last_applied_theme: Option<ThemePreference>,

    // Authentication validation state (not persisted)
    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub needs_auth_validation: bool,

    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub stored_auth_state: Option<AuthState>,

    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub validation_in_progress: bool,

    #[cfg(not(target_arch = "wasm32"))]
    #[serde(skip)]
    pub current_authenticator: Option<Arc<crate::auth_native::NativeAuthenticator>>,
}

impl Default for WardaFrontend {
    fn default() -> Self {
        Self {
            shared_data: SharedData::default(),
            current_tab: Tab::default(),
            show_preferences: false,
            theme_preference: ThemePreference::default(),
            last_applied_theme: None,
            #[cfg(not(target_arch = "wasm32"))]
            needs_auth_validation: false,
            #[cfg(not(target_arch = "wasm32"))]
            stored_auth_state: None,
            #[cfg(not(target_arch = "wasm32"))]
            validation_in_progress: false,
            #[cfg(not(target_arch = "wasm32"))]
            current_authenticator: None,
        }
    }
}

impl WardaFrontend {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        log::info!("Initializing Warda Frontend");

        let mut app: Self = if let Some(storage) = cc.storage {
            log::debug!("Loading app state from storage");
            let mut loaded_app: Self = eframe::get_value(storage, eframe::APP_KEY).unwrap_or_default();

            // For native builds, validate stored authentication state
            #[cfg(not(target_arch = "wasm32"))]
            {
                if loaded_app.shared_data.auth_state.is_authenticated {
                    log::info!("Found stored authentication state, will validate on startup");
                    // Store the original auth state for validation
                    loaded_app.stored_auth_state = Some(loaded_app.shared_data.auth_state.clone());
                    // Mark that we need to validate the stored auth state
                    loaded_app.shared_data.auth_state.is_authenticated = false; // Temporarily set to false until validated
                    loaded_app.needs_auth_validation = true;
                }
            }

            loaded_app
        } else {
            log::debug!("No storage available, using default state");
            Default::default()
        };

        // Try to get Mapbox token from environment, fallback to OpenStreetMap
        log::info!("mapbox access token: {:#?}",std::env::var("MAPBOX_ACCESS_TOKEN"));
        let tiles = if let Ok(token) = std::env::var("MAPBOX_ACCESS_TOKEN") {
            if !token.is_empty() {
                log::info!("Using Mapbox tiles with provided token");
                HttpTiles::new(Mapbox { style: Default::default(), high_resolution: false, access_token: token }, cc.egui_ctx.clone())
            } else {
                log::warn!("MAPBOX_ACCESS_TOKEN is empty, falling back to OpenStreetMap");
                HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
            }
        } else {
            log::info!("No MAPBOX_ACCESS_TOKEN found, using OpenStreetMap");
            HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
        };

        app.shared_data.tiles = Some(tiles);

        // Apply the stored theme preference immediately to the context
        log::info!("Applying stored theme preference: {:?}", app.theme_preference);
        match app.theme_preference {
            ThemePreference::Light => {
                cc.egui_ctx.set_visuals(egui::Visuals::light());
                log::info!("Applied light theme during initialization");
            }
            ThemePreference::Dark => {
                cc.egui_ctx.set_visuals(egui::Visuals::dark());
                log::info!("Applied dark theme during initialization");
            }
            ThemePreference::System => {
                // For now, default to light theme as system detection is complex
                cc.egui_ctx.set_visuals(egui::Visuals::light());
                log::info!("Applied system theme (defaulting to light) during initialization");
            }
        }
        app.last_applied_theme = Some(app.theme_preference);

        // Initialize authentication state
        app.update_auth_state();

        log::debug!("Warda Frontend initialized successfully");

        app
    }

    /// Update authentication state
    fn update_auth_state(&mut self) {
        #[cfg(target_arch = "wasm32")]
        {
            self.shared_data.auth_state = get_auth_state();
        }

        #[cfg(not(target_arch = "wasm32"))]
        {
            // Check for authentication results from the channel
            if let Some(ref receiver) = self.shared_data.auth_receiver {
                if let Ok(result) = receiver.try_recv() {
                    // Handle both login and validation results
                    if self.validation_in_progress {
                        self.validation_in_progress = false;
                        self.needs_auth_validation = false;
                        match result {
                            Ok(auth_state) => {
                                log::info!("Stored authentication state validation successful");
                                self.shared_data.auth_state = auth_state;
                            }
                            Err(error) => {
                                log::warn!("Stored authentication state validation failed: {}", error);
                                self.shared_data.auth_state = AuthState::default();
                                self.stored_auth_state = None;
                            }
                        }
                        // Clear authenticator reference after validation
                        self.current_authenticator = None;
                    } else {
                        self.shared_data.login_in_progress = false;
                        match result {
                            Ok(auth_state) => {
                                log::info!("Authentication successful");
                                self.shared_data.auth_state = auth_state;
                            }
                            Err(error) => {
                                log::error!("Authentication failed: {}", error);
                                self.shared_data.auth_state = AuthState::default();
                            }
                        }
                        // Clear authenticator reference after authentication
                        self.current_authenticator = None;
                    }
                }
            }

            // Handle stored authentication state validation
            if self.needs_auth_validation && !self.validation_in_progress {
                self.start_auth_validation();
            }
        }
    }

    /// Apply the current theme preference to the UI if it has changed
    fn apply_theme_if_needed(&mut self, ctx: &Context) {
        // Check if the current visuals match our preference
        let current_is_dark = ctx.style().visuals.dark_mode;
        let should_be_dark = match self.theme_preference {
            ThemePreference::Dark => true,
            ThemePreference::Light => false,
            ThemePreference::System => false, // Default to light for now
        };

        if current_is_dark != should_be_dark || self.last_applied_theme != Some(self.theme_preference) {
            match self.theme_preference {
                ThemePreference::Light => {
                    ctx.set_visuals(egui::Visuals::light());
                    log::debug!("Applied light theme (was dark: {})", current_is_dark);
                }
                ThemePreference::Dark => {
                    ctx.set_visuals(egui::Visuals::dark());
                    log::debug!("Applied dark theme (was dark: {})", current_is_dark);
                }
                ThemePreference::System => {
                    // Try to detect system theme preference
                    // For now, default to light theme as system detection is complex
                    // In the future, this could use platform-specific APIs
                    ctx.set_visuals(egui::Visuals::light());
                    log::debug!("Applied system theme (defaulting to light, was dark: {})", current_is_dark);
                }
            }
            self.last_applied_theme = Some(self.theme_preference);
        }
    }

    /// Apply the current theme preference to the UI (force apply)
    fn apply_theme(&mut self, ctx: &Context) {
        self.last_applied_theme = None; // Force reapplication
        self.apply_theme_if_needed(ctx);
    }

    #[cfg(not(target_arch = "wasm32"))]
    fn start_native_login(&mut self) {
        use crate::auth_native::NativeAuthenticator;

        if self.shared_data.login_in_progress {
            log::warn!("Login already in progress");
            return;
        }

        self.shared_data.login_in_progress = true;
        log::info!("Starting native authentication process");

        // Create a channel for communication
        let (sender, receiver) = mpsc::channel();
        self.shared_data.auth_receiver = Some(receiver);

        // Create authenticator and store reference for cancellation
        match NativeAuthenticator::new() {
            Ok(authenticator) => {
                let authenticator = Arc::new(authenticator);
                self.current_authenticator = Some(authenticator.clone());

                // Spawn authentication in a separate thread
                std::thread::spawn(move || {
                    let rt = tokio::runtime::Runtime::new().unwrap();
                    rt.block_on(async {
                        match authenticator.authenticate().await {
                            Ok(auth_state) => {
                                let _ = sender.send(Ok(auth_state));
                            }
                            Err(e) => {
                                let _ = sender.send(Err(e.to_string()));
                            }
                        }
                    });
                });
            }
            Err(e) => {
                log::error!("Failed to initialize authenticator: {}", e);
                self.shared_data.login_in_progress = false;
                let _ = sender.send(Err(format!("Failed to initialize authenticator: {}", e)));
            }
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    fn start_auth_validation(&mut self) {
        use crate::auth_native::NativeAuthenticator;

        if self.validation_in_progress {
            log::warn!("Validation already in progress");
            return;
        }

        let Some(stored_auth_state) = self.stored_auth_state.clone() else {
            log::warn!("No stored auth state to validate");
            self.needs_auth_validation = false;
            return;
        };

        self.validation_in_progress = true;
        log::info!("Starting validation of stored authentication state");

        // Create a channel for validation results
        let (sender, receiver) = mpsc::channel();
        self.shared_data.auth_receiver = Some(receiver);

        // Spawn validation in a separate thread
        std::thread::spawn(move || {
            let rt = tokio::runtime::Runtime::new().unwrap();
            rt.block_on(async {
                match NativeAuthenticator::new() {
                    Ok(authenticator) => {
                        match authenticator.validate_stored_auth_state(&stored_auth_state).await {
                            Ok(is_valid) => {
                                if is_valid {
                                    log::info!("Stored authentication state is valid");
                                    let _ = sender.send(Ok(stored_auth_state));
                                } else {
                                    log::warn!("Stored authentication state is invalid");
                                    let _ = sender.send(Err("Stored authentication state is invalid".to_string()));
                                }
                            }
                            Err(e) => {
                                log::error!("Failed to validate stored authentication state: {}", e);
                                let _ = sender.send(Err(format!("Validation failed: {}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("Failed to initialize authenticator for validation: {}", e);
                        let _ = sender.send(Err(format!("Failed to initialize authenticator: {}", e)));
                    }
                }
            });
        });
    }

    #[cfg(not(target_arch = "wasm32"))]
    fn cancel_authentication(&mut self) {
        log::info!("Cancelling authentication process");

        // Cancel the current authenticator if it exists
        if let Some(ref authenticator) = self.current_authenticator {
            authenticator.cancel();
        }

        // Reset authentication state
        self.shared_data.login_in_progress = false;
        self.validation_in_progress = false;
        self.needs_auth_validation = false;

        // Clear the receiver channel to stop listening for auth results
        self.shared_data.auth_receiver = None;

        // Clear any stored auth state that was being validated
        self.stored_auth_state = None;

        // Clear the authenticator reference
        self.current_authenticator = None;

        // Ensure we're in unauthenticated state
        self.shared_data.auth_state = AuthState::default();

        log::info!("Authentication cancelled - ready for new login attempt");
    }
}

// WASM bindings for authentication
#[cfg(target_arch = "wasm32")]
fn get_auth_state() -> AuthState {
    use wasm_bindgen::JsCast;

    let window = web_sys::window().expect("No window");

    // Check if user is authenticated using the exposed JS function
    let is_authenticated_fn = js_sys::Reflect::get(&window, &JsValue::from_str("isAuthenticated"))
        .unwrap_or(JsValue::NULL);

    let is_authenticated = if let Ok(func) = is_authenticated_fn.dyn_into::<js_sys::Function>() {
        func.call0(&window)
            .unwrap_or(JsValue::FALSE)
            .as_bool()
            .unwrap_or(false)
    } else {
        false
    };

    if !is_authenticated {
        return AuthState::default();
    }

    // Get user info using the exposed JS function
    let get_user_info_fn = js_sys::Reflect::get(&window, &JsValue::from_str("getUserInfo"))
        .unwrap_or(JsValue::NULL);

    let user_info = if let Ok(func) = get_user_info_fn.dyn_into::<js_sys::Function>() {
        func.call0(&window).unwrap_or(JsValue::NULL)
    } else {
        JsValue::NULL
    };

    let mut auth_state = AuthState {
        is_authenticated: true,
        username: None,
        email: None,
        token: None,
    };

    // Extract username and email from user info
    if !user_info.is_null() && !user_info.is_undefined() {
        // Try to get preferred_username
        if let Ok(username) = js_sys::Reflect::get(&user_info, &JsValue::from_str("preferred_username")) {
            if let Some(username_str) = username.as_string() {
                auth_state.username = Some(username_str);
            }
        }

        // Try to get email
        if let Ok(email) = js_sys::Reflect::get(&user_info, &JsValue::from_str("email")) {
            if let Some(email_str) = email.as_string() {
                auth_state.email = Some(email_str);
            }
        }
    }

    // Get token using the exposed JS function
    let get_auth_token_fn = js_sys::Reflect::get(&window, &JsValue::from_str("getAuthToken"))
        .unwrap_or(JsValue::NULL);

    if let Ok(func) = get_auth_token_fn.dyn_into::<js_sys::Function>() {
        if let Ok(token) = func.call0(&window) {
            if let Some(token_str) = token.as_string() {
                auth_state.token = Some(token_str);
            }
        }
    }

    auth_state
}

#[cfg(target_arch = "wasm32")]
fn logout() {
    let window = web_sys::window().expect("No window");

    // Call the exposed logout function
    let logout_fn = js_sys::Reflect::get(&window, &JsValue::from_str("logoutUser"))
        .unwrap_or(JsValue::NULL);

    if let Ok(func) = logout_fn.dyn_into::<js_sys::Function>() {
        let _ = func.call0(&window);
    }
}

impl App for WardaFrontend {
    fn update(&mut self, ctx: &Context, _frame: &mut Frame) {
        // Apply theme preference if it has changed
        self.apply_theme_if_needed(ctx);

        // Update authentication state periodically
        #[cfg(target_arch = "wasm32")]
        {
            // Update auth state every few frames to keep it current
            static mut FRAME_COUNT: u32 = 0;
            unsafe {
                FRAME_COUNT += 1;
                if FRAME_COUNT % 60 == 0 { // Update every ~1 second at 60fps
                    self.update_auth_state();
                }
            }
        }

        // For native builds, check authentication results every frame
        #[cfg(not(target_arch = "wasm32"))]
        {
            self.update_auth_state();
        }

        // Log tab switches
        static mut LAST_TAB: Option<Tab> = None;
        unsafe {
            if LAST_TAB != Some(self.current_tab) {
                log::debug!("Switched to tab: {}", self.current_tab.name());
                LAST_TAB = Some(self.current_tab);
            }
        }

        let is_web = cfg!(target_arch = "wasm32");

        // Authentication status
        if self.shared_data.auth_state.is_authenticated {
            egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
                egui::MenuBar::new().ui(ui, |ui| {
                    ui.menu_button("File", |ui| {
                        if ui.button("Preferences").clicked() {
                            self.show_preferences = true;
                            ui.close();
                        }
                        if ui.button("Logout").clicked() {
                            log::info!("User requested logout");
                            #[cfg(target_arch = "wasm32")]
                            logout();

                            #[cfg(not(target_arch = "wasm32"))]
                            {
                                // For native, just clear the auth state
                                self.shared_data.auth_state = AuthState::default();
                                log::info!("User logged out");
                            }
                        }
                        if is_web {
                            if ui.button("Quit").clicked() {
                                log::info!("User requested quit");
                                ctx.send_viewport_cmd(egui::ViewportCommand::Close);
                            }
                        }
                    });
                    ui.menu_button("Edit", |ui| {
                        if ui.button("Copy").clicked() {

                        }
                        if ui.button("Paste").clicked() {

                        }
                    });
                    ui.add_space(16.0);
                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        // Show logout button
                        if ui.button("🚪 Logout").clicked() {
                            log::info!("User clicked logout");
                            #[cfg(target_arch = "wasm32")]
                            logout();

                            #[cfg(not(target_arch = "wasm32"))]
                            {
                                // For native, just clear the auth state
                                self.shared_data.auth_state = AuthState::default();
                                log::info!("User logged out");
                            }
                        }

                        // Show username or email
                        let display_name = self.shared_data.auth_state.username
                            .as_ref()
                            .or(self.shared_data.auth_state.email.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("");

                        ui.label(format!("👤 {}", display_name));
                    });
                    // Show egui's built-in theme preference buttons for debugging
                    egui::widgets::global_theme_preference_buttons(ui);
                });
            });
            // Tab bar
            egui::TopBottomPanel::top("tab_bar").show(ctx, |ui| {
                ui.horizontal(|ui| {
                    ui.spacing_mut().item_spacing.x = 0.0;

                    for tab in [Tab::Counter, Tab::TextEditor, Tab::DataList, Tab::Map] {
                        let selected = self.current_tab == tab;
                        if ui.selectable_label(selected, tab.name()).clicked() {
                            log::debug!("User clicked tab: {}", tab.name());
                            self.current_tab = tab;
                        }
                    }
                });
            });

            // Main content area
            CentralPanel::default().show(ctx, |ui| {
                match self.current_tab {
                    Tab::Counter => show_counter_view(ui, &mut self.shared_data),
                    Tab::TextEditor => show_text_editor_view(ui, &mut self.shared_data),
                    Tab::DataList => show_data_list_view(ui, &mut self.shared_data),
                    Tab::Map => show_map_view(ui, &mut self.shared_data),
                }
            });

            // Show preferences window if requested
            if self.show_preferences {
                let mut theme_changed = false;
                let mut new_theme = self.theme_preference;

                egui::Window::new("Preferences")
                    .open(&mut self.show_preferences)
                    .show(ctx, |ui| {
                        ui.heading("Application Preferences");
                        ui.separator();

                        ui.label("Theme:");
                        ui.horizontal(|ui| {
                            if ui.selectable_label(
                                new_theme == ThemePreference::Light,
                                "Light"
                            ).clicked() {
                                new_theme = ThemePreference::Light;
                                theme_changed = true;
                            }

                            if ui.selectable_label(
                                new_theme == ThemePreference::Dark,
                                "Dark"
                            ).clicked() {
                                new_theme = ThemePreference::Dark;
                                theme_changed = true;
                            }

                            if ui.selectable_label(
                                new_theme == ThemePreference::System,
                                "System"
                            ).clicked() {
                                new_theme = ThemePreference::System;
                                theme_changed = true;
                            }
                        });

                        ui.separator();
                        ui.label("More preferences coming soon...");
                    });

                // Apply theme changes outside the closure to avoid borrowing issues
                if theme_changed {
                    self.theme_preference = new_theme;
                    self.apply_theme(ctx);
                    log::debug!("Theme preference changed to: {:?}", self.theme_preference);
                }
            }
        } else {
            // Show login screen when not authenticated
            CentralPanel::default().show(ctx, |ui| {
                self.show_login_screen(ui);
            });
        }
    }

    fn save(&mut self, storage: &mut dyn eframe::Storage) {
        log::debug!("Saving app state to storage");
        eframe::set_value(storage, eframe::APP_KEY, self);
    }
}

impl WardaFrontend {
    fn show_login_screen(&mut self, ui: &mut egui::Ui) {
        ui.vertical_centered(|ui| {
            ui.add_space(100.0);

            // App logo/title
            ui.heading("🗺️ Warda");
            ui.add_space(20.0);

            ui.label("Welcome to Warda - Interactive Map Application");
            ui.add_space(40.0);

            // Authentication status
            #[cfg(not(target_arch = "wasm32"))]
            {
                if self.shared_data.login_in_progress {
                    ui.add_space(20.0);
                    ui.label("🔄 Authenticating...");
                    ui.add_space(10.0);
                    ui.label("Please complete the login in your browser.");
                    ui.add_space(10.0);
                    ui.label("This window will update automatically once you're authenticated.");
                    ui.add_space(20.0);

                    // Add cancel button
                    ui.horizontal(|ui| {
                        ui.add_space(50.0); // Center the button
                        if ui.button("❌ Cancel Authentication").clicked() {
                            log::info!("User cancelled authentication");
                            self.cancel_authentication();
                        }
                    });

                    ui.add_space(10.0);
                    ui.label("Click cancel if your browser crashed or closed.");
                } else {
                    ui.label("Please log in to access the application.");
                    ui.add_space(20.0);

                    if ui.button("🔐 Login with Keycloak").clicked() {
                        log::info!("User clicked login from login screen");
                        self.start_native_login();
                    }

                    ui.add_space(20.0);
                    ui.label("This will open your browser to authenticate with Keycloak.");
                }
            }

            #[cfg(target_arch = "wasm32")]
            {
                ui.label("🔒 Authentication required");
                ui.add_space(10.0);
                ui.label("Please ensure you are logged in to access the application.");
            }
        });
    }
}

// View functions for each tab

fn show_counter_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Counter View");
    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
    });

    ui.horizontal(|ui| {
        if ui.button("Increment").clicked() {
            shared_data.counter += 1;
            log::debug!("Counter incremented to: {}", shared_data.counter);
        }
        if ui.button("Decrement").clicked() {
            shared_data.counter -= 1;
            log::debug!("Counter decremented to: {}", shared_data.counter);
        }
        if ui.button("Reset").clicked() {
            log::info!("Counter reset from {} to 0", shared_data.counter);
            shared_data.counter = 0;
        }
    });

    ui.separator();

    ui.label("Shared slider (affects other tabs):");
    let old_value = shared_data.slider_value;
    ui.add(egui::Slider::new(&mut shared_data.slider_value, 0.0..=10.0).text("value"));
    if (old_value - shared_data.slider_value).abs() > 0.01 {
        log::debug!("Slider value changed from {:.1} to {:.1}", old_value, shared_data.slider_value);
    }

    let old_checkbox = shared_data.checkbox_state;
    ui.checkbox(&mut shared_data.checkbox_state, "Shared checkbox");
    if old_checkbox != shared_data.checkbox_state {
        log::debug!("Checkbox toggled to: {}", shared_data.checkbox_state);
    }

    ui.separator();
    ui.label("💡 Try switching tabs to see how state persists!");
}

fn show_text_editor_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Text Editor View");
    ui.separator();

    ui.label("This text is shared across all tabs:");
    ui.text_edit_multiline(&mut shared_data.text);

    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Counter from other tab:");
        ui.label(shared_data.counter.to_string());
        if ui.button("+1").clicked() {
            shared_data.counter += 1;
        }
    });

    ui.horizontal(|ui| {
        ui.label("Slider value:");
        ui.label(format!("{:.1}", shared_data.slider_value));
    });

    if shared_data.checkbox_state {
        ui.colored_label(egui::Color32::GREEN, "✓ Checkbox is checked!");
    } else {
        ui.colored_label(egui::Color32::RED, "✗ Checkbox is unchecked");
    }
}

fn show_data_list_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Data List View");
    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Add item:");
        let mut new_item = String::new();
        if ui.text_edit_singleline(&mut new_item).lost_focus()
            && ui.input(|i| i.key_pressed(egui::Key::Enter))
            && !new_item.is_empty() {
            log::info!("Added new item: '{}'", new_item);
            shared_data.items.push(new_item);
        }
    });

    ui.separator();

    ui.label("Items:");
    let mut to_remove = None;
    for (i, item) in shared_data.items.iter().enumerate() {
        ui.horizontal(|ui| {
            ui.label(format!("{}. {}", i + 1, item));
            if ui.small_button("Remove").clicked() {
                log::info!("Removing item: '{}'", item);
                to_remove = Some(i);
            }
        });
    }

    if let Some(index) = to_remove {
        shared_data.items.remove(index);
    }

    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        ui.label("| Slider:");
        ui.label(format!("{:.1}", shared_data.slider_value));
    });
}

fn show_map_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Map View");
    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        if ui.button("Add marker count").clicked() {
            shared_data.counter += 1;
            log::debug!("Map view: Counter incremented to {}", shared_data.counter);
        }
    });

    ui.separator();

    if let Some(ref mut tiles) = shared_data.tiles {
        ui.add(Map::new(
            Some(tiles),
            &mut shared_data.map_memory,
            lon_lat(17.03664, 51.09916)
        ));
    } else {
        log::error!("Map tiles not initialized");
        ui.label("Map tiles not initialized");
    }
}